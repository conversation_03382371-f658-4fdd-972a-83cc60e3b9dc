# File Transfer SDK

一个基于Spring Boot的、生产级的高性能文件传输SDK，提供分块上传、断点续传、秒传、多用户管理、权限控制、动态限速、数据库容错和Web管理界面等一系列强大功能。

## 核心特性

- **高性能传输**: 支持分块上传与多线程分块下载，充分利用网络带宽。
- **断点续传**: 上传或下载中断后可从断点无缝续传，支持基于HTTP Range的下载。
- **智能秒传**: 基于文件MD5进行秒传判断，通过物理文件校验确保文件一致性，避免误传。
- **多用户与权限**: 支持多用户隔离，可为不同用户配置独立的存储路径、密钥和权限。
- **动态速率限制**: 可为每个用户独立设置上传和下载速率限制，并可在服务运行时动态调整。
- **数据库容错**: 即使数据库服务异常，系统仍能基于文件系统的元数据信息提供下载服务，保证核心可用性。
- **丰富的管理功能**: 提供API进行数据库健康检查、备份、以及从物理文件反向重建数据库。
- **无状态认证**: 基于 `HMAC-SHA256` 的无状态认证机制，安全且易于集成。

## 1. 服务端配置 (`application.yml`)

通过 `file.transfer.server` 前缀进行配置。系统支持全局默认配置和针对特定用户的覆盖配置。

### 配置示例

以下示例展示了如何定义一个默认配置，并为 `admin` 和 `guest` 两个特定用户设置不同的权限。

```yaml
file:
  transfer:
    server:
      # =======================================
      # 全局服务配置
      # =======================================
      enabled: true
      # SQLite数据库文件路径
      database-path: ./data/file-transfer.db
      # 服务监听端口
      server-port: 49011
      # 应用上下文路径
      context-path: /file-transfer

      # =======================================
      # 默认用户配置 (所有用户的默认值)
      # =======================================
      default-config:
        # 默认为普通用户角色
        role: "user"
        # 默认存储路径
        storage-path: ./storage/default
        # 默认上传限速: 10 MB/s (单位: 字节/秒)
        upload-rate-limit: 10485760
        # 默认下载限速: 20 MB/s
        download-rate-limit: 20971520
        # 默认开启限速
        rate-limit-enabled: true
        # 默认分块大小: 5 MB
        default-chunk-size: 5242880
        # 默认最大文件大小: 10 GB
        max-file-size: 10737418240
        # 默认开启秒传
        fast-upload-enabled: true

      # =======================================
      # 特定用户配置 (按用户名定义，会覆盖默认配置)
      # =======================================
      users:
        # "admin" 用户拥有更高权限和独立存储路径
        admin:
          role: "admin"                 # 管理员角色
          secret-key: "a-very-secret-key-for-admin"
          storage-path: ./storage/admin
          upload-rate-limit: 104857600   # 上传限速: 100 MB/s
          download-rate-limit: 104857600 # 下载限速: 100 MB/s
        
        # "guest" 用户关闭了速率限制功能，且文件大小受限
        guest:
          role: "user"                  # 普通用户角色
          secret-key: "a-simple-key-for-guest"
          rate-limit-enabled: false
          max-file-size: 104857600 # 最大文件: 100 MB
```

### 配置逻辑

- 当一个用户（例如 `guest`）的配置被请求时，系统会加载该用户的特定配置。
- 对于该用户未明确指定的任何属性（例如 `guest` 的 `storage-path`），系统会自动使用 `default-config` 中的对应值。
- 这套机制提供了极大的灵活性，既能为所有用户设置统一标准，也能为特定用户提供精细化控制。

## 2. 客户端配置

客户端通过编程方式进行配置，推荐使用 `ClientConfigBuilder` 来构建配置对象，提供简洁统一的配置接口。

### 简化配置示例（推荐）

```java
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.FileTransferClient;

// 方式1：使用Builder进行详细配置（推荐）
ClientConfig clientConfig = ClientConfigBuilder.create()
    .serverAddr("127.0.0.1")
    .serverPort(49011)
    .contextPath("file-transfer") // 与服务端 context-path 匹配
    .auth("admin", "a-very-secret-key-for-admin")
    .chunkSize(5 * 1024 * 1024L) // 5MB 分块
    .timeouts(120) // 统一设置超时时间为120秒
    .maxConcurrentTransfers(3) // 并发传输数
    .retry(5, 1000) // 5次重试，间隔1秒
    .build();

// 方式2：使用便捷方法快速配置
ClientConfig quickConfig = ClientConfigBuilder.quickConnect(
    "127.0.0.1", "admin", "a-very-secret-key-for-admin");

// 方式3：使用预设的生产环境配置
ClientConfig prodConfig = ClientConfigBuilder.productionConfig(
    "prod.example.com", 443, "admin", "a-very-secret-key-for-admin");

// 创建客户端实例
FileTransferClient client = new FileTransferClient(clientConfig);

// 后续可使用 client.uploadFile(...) 或 client.downloadFile(...)
```

### 高级配置示例

```java
// 高性能配置 - 适用于大文件传输场景
ClientConfig highPerfConfig = ClientConfigBuilder.create()
    .serverAddr("fast.example.com")
    .auth("user", "secret")
    .chunkSize(8 * 1024 * 1024L) // 8MB大分块
    .maxConcurrentTransfers(8) // 高并发
    .timeouts(180) // 更长超时时间
    .retry(3, 500) // 快速重试
    .build();

// 或使用预设的高性能配置
ClientConfig fastConfig = ClientConfigBuilder.highPerformanceConfig(
    "fast.example.com", "user", "secret");
```



## 3. API 接口

所有API都以服务端配置的 `context-path` 为前缀，默认为 `/file-transfer`。

### 3.1 认证机制

所有需要认证的接口都必须在HTTP请求头中包含以下两个字段：

- `X-File-Transfer-User`: 用户名 (例如: `admin`)
- `X-File-Transfer-Auth`: 认证令牌

**认证令牌生成算法 (客户端实现):**

1. 获取当前时间的毫秒级时间戳 `timestamp`。
2. 构造待签名字符串 `data = username + ":" + timestamp`。
3. 使用用户的 `secretKey` 对 `data` 进行 `HMAC-SHA256` 加密，并将结果进行 `Base64` 编码，得到 `signature`。
4. 最终的认证令牌为 `token = username + ":" + timestamp + ":" + signature`。

服务端会对收到的令牌进行相同的计算和比对，并检查时间戳是否在5分钟有效期内，以防止重放攻击。

### 3.2 文件传输接口

基础路径: `/api/file`

#### **上传流程**

**1. `POST /upload/init` - 初始化上传**

- **描述**: 开始一个文件上传任务，服务端会检查秒传可能性，并返回一个唯一的 `transferId`。
- **请求体**: `application/json`
  ```json
  {
      "originalFileName": "mydocument.zip", // 客户端原始文件名
      "fileExtension": "zip", // 文件后缀名 (可选, 若不提供会从originalFileName中提取)
      "fileSize": 10485760, // 文件总大小 (字节)
      "fileMd5": "d41d8cd98f00b204e9800998ecf8427e", // 完整文件的MD5
      "chunkSize": 5242880 // 分块大小 (字节, 可选)
  }
  ```
- **响应体**:
  ```json
  {
      "code": 200,
      "data": {
          "transferId": "a1b2c3d4-e5f6-...", // 本次传输的唯一ID
          "fileId": "01H...", // 文件的ULID标识
          "fileName": "d41d8cd98f00b204e9800998ecf8427e.zip", // 服务端存储名
          "chunkSize": 5242880,
          "totalChunks": 2,
          "fastUpload": false, // 是否已通过秒传完成
          "uploadedChunks": [] // 已上传的分块列表 (用于断点续传)
      }
  }
  ```

**2. `POST /upload/chunk` - 上传文件分块**

- **描述**: 上传一个文件分块，请求体为 `multipart/form-data`。
- **表单参数**:
    - `transferId`: 初始化时获取的传输ID。
    - `chunkIndex`: 分块的序号 (从0开始)。
    - `chunkMd5`: 当前分块数据的MD5。
    - `chunk`: 文件分块的二进制数据。
- **响应体**:
  ```json
  {
      "code": 200,
      "message": "分块上传成功"
  }
  ```

**3. `POST /upload/complete/{transferId}` - 完成文件上传**

- **描述**: 所有分块上传完毕后，通知服务端进行文件合并与最终校验。
- **路径参数**: `transferId`
- **响应体**:
  ```json
  {
      "code": 200,
      "message": "文件上传完成",
      "data": {
          "transferId": "a1b2c3d4-e5f6-...",
          "fileId": "01H...",
          "fileName": "d41d8cd98f00b204e9800998ecf8427e.zip",
          "relativePath": "202405/01H.../d41d8cd98f00b204e9800998ecf8427e.zip",
          "fileSize": 10485760,
          "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
          "completeTime": "2024-05-20T10:00:00Z",
          "transferDuration": 5000 // 传输耗时(毫秒)
      }
  }
  ```

#### **下载流程**

**1. `GET /download/info/{fileId}` - 获取文件信息**

- **描述**: 在下载前获取文件的元数据。
- **路径参数**: `fileId`
- **响应体**:
  ```json
  {
      "code": 200,
      "data": {
          "fileId": "01H...",
          "fileName": "mydocument.zip",
          "fileSize": 10485760,
          "uploadTime": "2024-05-20T10:00:00Z"
      }
  }
  ```

**2. `GET /download/chunk/{fileId}` - 分块下载文件**

- **描述**: 下载文件的指定部分，支持 `Range` 请求头，是实现多线程下载和断点续传的基础。
- **路径参数**: `fileId`
- **请求头**: `Range: bytes=0-5242879` (请求文件的前5MB)
- **成功响应**:
    - **状态码**: `206 Partial Content`
    - **响应头**: `Content-Range: bytes 0-5242879/10485760`
    - **响应体**: 文件的二进制数据流。

#### **其他接口**

- **`GET /progress/{transferId}`**: 查询上传进度。
- **`GET /health`**: 简单的服务健康检查，无需认证。

### 3.3 数据库管理接口

基础路径: `/api/database` (需要管理员权限)

- **`GET /health`**: 检查数据库的详细健康状态。
- **`POST /backup`**: 创建当前数据库的备份文件。
- **`GET /backups`**: 列出所有可用的数据库备份。
- **`GET /backup/download/{fileName}`**: 下载指定的备份文件。
- **`POST /rebuild`**: 从磁盘上的 `info.json` 元数据文件反向扫描，重建整个文件数据库。

### 3.4 系统管理接口

基础路径: `/api/admin` (需要管理员权限)

- **`GET /statistics`**: 获取实时的传输统计信息（活跃连接、总流量等）。
- **`GET /health`**: 获取详细的系统健康状况，包括JVM内存使用情况。
- **`GET /clear-rate-limiters`**: 清空所有用户的速率限制器缓存，可用于动态更新配置后立即生效。

## 4. 核心功能逻辑详解

### 数据库容错 (回落) 机制

这是保障服务高可用的核心设计。
1.  **元数据冗余**: 每个文件成功上传后，除了在SQLite数据库中记录信息，还会在其存储目录（例如 `storage/202405/01H.../`）下额外创建一个 `info.json` 文件，其中包含了文件的完整元数据（原始文件名、大小、MD5等）。
2.  **健康检查**: 在处理下载请求时，服务会首先检查数据库的健康状况。
3.  **自动回落**:
    - 如果数据库**不健康**，下载服务会**跳过数据库**，直接进入回落模式。
    - 如果数据库**健康**但在其中**未找到**文件记录，服务同样会触发回落模式。
4.  **回落模式工作流**: 服务会根据请求的 `fileId` 和存储规则（ULID -> `YYYYMM/fileId`）直接在文件系统中定位 `info.json` 文件。找到后，从中读取元数据，并提供文件下载服务。
5.  **灾难恢复**: 当数据库完全损坏时，可以调用 `POST /api/database/rebuild` 接口。该接口会遍历所有存储目录，读取所有 `info.json` 文件，并用这些信息重建整个SQLite数据库，实现服务的快速恢复。

### 用户鉴权与限速

- **鉴权**: 如 "认证机制" 部分所述，通过 `X-File-Transfer-User` 和 `X-File-Transfer-Auth` 请求头进行HMAC签名验证。
- **限速**:
    - **实现**: 基于 Google Guava 的 `RateLimiter` 实现。
    - **粒度**: 为每个用户的 `upload` 和 `download` 操作独立创建限速器。
    - **应用**: 在上传和下载的数据流处理循环中，通过 `acquire()` 方法精确控制数据通过速率，从而匹配配置的字节/秒限制。
    - **动态性**: 限速配置在用户请求时动态加载，并且可以通过 `/api/admin/clear-rate-limiters` 接口清空缓存，使配置变更即时生效。

### 文件标识符（`fileId`）的唯一性与生命周期

系统设计确保了每个文件在服务端的标识符 (`fileId`) 都是唯一的，从根本上杜绝了因 `fileId` 重复导致的数据混乱或覆盖问题。

- **服务端生成**: `fileId` **完全由服务端在 `POST /upload/init` 接口被调用时生成**。客户端不参与 `fileId` 的创建，也无法指定或复用一个已知的 `fileId` 进行上传。
- **唯一性保障**: 系统采用 **ULID (Universally Unique Lexicographically Sortable Identifier)** 算法生成 `fileId`。ULID 结合了高精度的时间戳和随机数，能有效保证在高并发场景下的唯一性。
- **独立的生命周期**: **每一次文件上传（包括秒传）都会获得一个全新的、独立的 `fileId`**。即使是秒传，其后端逻辑也是为新上传的记录创建一个新的 `fileId`，然后将已存在的文件内容复制到新 `fileId` 对应的存储路径下。这保证了每个上传任务都有清晰、独立的生命周期和物理存储位置。

这个机制使得每个文件都拥有一个从创建到删除的完整、隔离的生命周期，确保了系统的健壮性和数据一致性。

## 5. 代码文件目录结构说明

```
file-transfer-sdk/
├── file-transfer-server-sdk/           # 服务端SDK核心模块
│   ├── src/main/java/com/sdesrd/filetransfer/server/
│   │   ├── controller/                 # REST API控制器层
│   │   │   ├── FileTransferController.java      # 文件传输核心接口
│   │   │   ├── FileTransferAdminController.java # 管理接口（状态查询等）
│   │   │   └── DatabaseManagementController.java # 数据库管理接口
│   │   ├── service/                    # 业务逻辑服务层
│   │   │   ├── FileTransferService.java         # 文件传输核心服务
│   │   │   ├── AuthService.java                # 认证服务（API签名验证）
│   │   │   └── DatabaseFallbackService.java    # 数据库容错服务
│   │   ├── interceptor/                # 拦截器
│   │   │   └── AuthInterceptor.java            # 认证拦截器（角色权限控制）
│   │   ├── config/                     # 配置类
│   │   │   ├── FileTransferProperties.java     # 主配置类
│   │   │   ├── UserConfig.java                # 用户配置（含角色管理）
│   │   │   └── FileTransferAutoConfiguration.java # 自动配置
│   │   ├── entity/                     # 数据实体
│   │   │   └── FileTransferRecord.java         # 文件传输记录实体
│   │   ├── mapper/                     # 数据访问层
│   │   │   └── FileTransferRecordMapper.java   # MyBatis映射器
│   │   ├── dto/                        # 数据传输对象
│   │   ├── exception/                  # 异常类
│   │   │   ├── FileTransferException.java      # 基础异常类
│   │   │   └── FileIntegrityException.java     # 文件完整性异常
│   │   └── util/                       # 工具类
│   │       ├── UlidUtils.java                  # ULID工具类
│   │       ├── FileUtils.java                  # 文件操作工具
│   │       └── FilePermissionUtils.java       # 文件权限管理工具
│   └── src/test/java/                  # 单元测试
├── file-transfer-client-sdk/           # 客户端SDK模块
│   ├── src/main/java/com/sdesrd/filetransfer/client/
│   │   ├── FileTransferClient.java             # 客户端主类
│   │   ├── config/                     # 客户端配置
│   │   ├── dto/                        # 数据传输对象
│   │   └── util/                       # 客户端工具类
│   └── src/test/java/                  # 客户端测试
├── file-transfer-client-demo/          # 客户端演示应用
│   └── src/main/java/
│       └── FileTransferClientDemo.java        # 演示程序主类
├── file-transfer-server-standalone/    # 独立服务端应用
│   ├── src/main/java/
│   │   └── FileTransferServerApplication.java # 独立服务端启动类
│   └── start-server.sh                # 服务启动脚本
├── scripts/                           # 构建和测试脚本
│   ├── build-and-test.sh             # 自动化构建测试脚本
│   ├── java-environment-check.sh     # Java环境检查脚本
│   └── set-java-env.sh               # Java环境设置脚本
└── docs/                             # 项目文档
```

## 6. 服务端磁盘文件存储目录结构说明

### 存储架构设计

文件传输SDK采用**全局共享数据库 + 用户独立文件存储**的架构设计：

#### 全局数据库存储
```
${database-path}/                       # 全局数据库文件位置（由database-path配置）
└── file-transfer.db                   # 所有用户共享的SQLite数据库文件
```

#### 用户文件存储（每个用户独立）
```
${user-storage-path}/                   # 用户专用存储目录（由用户配置的storage-path决定）
├── 202312/                            # 年月分区目录（YYYYMM格式）
│   ├── 01HN2Z8X9K7Q3M5P6R8S9T0V1W/    # ULID格式的fileId目录
│   │   ├── d41d8cd98f00b204e9800998ecf8427e.txt    # MD5.扩展名格式的实际文件
│   │   └── info.json                  # 文件元数据
│   ├── 01HN2Z9Y0L8R4N6Q7S9U1V2W3X/    # 另一个文件的ULID目录
│   │   └── a1b2c3d4e5f6789012345678901234567890.pdf
│   └── ...
├── 202401/                            # 下一个月的分区
├── ...
```

### 临时分块文件存储
上传分块文件会直接存储在对应的 fileId 目录下,每个分片文件的命名格式为：
```
${user-storage-path}/YYYYMM/{fileId}/
├── {md5}.{ext}                   # 合并完成后的最终文件
├── info.json                     # 文件元数据
├── {md5}.{ext}.chunk.000000      # 第一个分片（临时）
├── {md5}.{ext}.chunk.000001      # 第二个分片（临时）
└── ...                           # 其他分片
```
合并完成后，所有 `.chunk.*` 临时分片文件会被删除。

#### 完整系统目录结构示例
```
/data/                                 # 系统根目录
├── file-transfer/                     # 全局数据库目录
│   ├── database.db                    # 共享SQLite数据库（所有用户的传输记录）
│   └── backup/                        # 数据库备份目录
│       ├── database-20241201_120000.db
│       └── database-20241201_180000.db
├── admin/                             # 管理员用户的文件存储
│   ├── 202312/
│   │   └── 01HN2Z8X9K7Q3M5P6R8S9T0V1W/
│   │       └── d41d8cd98f00b204e9800998ecf8427e.txt
│   └── temp/
└── user1/                             # 普通用户user1的文件存储
    ├── 202312/
    │   └── 01HN2Z9Y0L8R4N6Q7S9U1V2W3X/
    │       └── a1b2c3d4e5f6789012345678901234567890.pdf
    └── temp/
```
