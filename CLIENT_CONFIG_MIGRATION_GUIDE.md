# 客户端配置迁移指南

**文件传输SDK从3.0.0版本开始提供了简化的统一配置接口**

## 概述

为了提升开发者体验，我们将原来分离的`ClientAuthConfig`和`ClientConfig`配置合并为统一的`ClientConfig`类。这样做的好处：

✅ **简化配置**: 所有配置项在同一层级，API更直观  
✅ **减少学习成本**: 不需要理解分层配置结构  
✅ **保持向后兼容**: 现有代码仍可正常运行  
✅ **新增便捷方法**: 提供预设配置，快速上手  

## 迁移对比

### 旧方式（已废弃，但仍支持）

```java
// 旧方式 - 需要先创建认证配置，再传入Builder
ClientAuthConfig authConfig = new ClientAuthConfig()
    .setServerAddr("127.0.0.1")
    .setServerPort(49011)
    .setContextPath("file-transfer")
    .setUser("admin")
    .setSecretKey("admin-secret-key");

ClientConfig config = new ClientConfigBuilder()
    .auth(authConfig)  // @Deprecated
    .chunkSize(5 * 1024 * 1024L)
    .connectTimeoutSeconds(30)
    .readTimeoutSeconds(120)
    .retryCount(5)
    .maxConcurrentTransfers(3)
    .build();
```

### 新方式（推荐）

```java
// 新方式 - 直接在Builder中配置所有参数
ClientConfig config = ClientConfigBuilder.create()
    .serverAddr("127.0.0.1")
    .serverPort(49011)
    .contextPath("file-transfer")
    .auth("admin", "admin-secret-key")  // 直接传用户名和密钥
    .chunkSize(5 * 1024 * 1024L)
    .timeouts(120)  // 统一设置所有超时时间
    .retry(5, 1000)  // 重试次数和间隔
    .maxConcurrentTransfers(3)
    .build();
```

## 迁移步骤

### 步骤1：更新依赖

确保使用3.0.0或更高版本：

```xml
<dependency>
    <groupId>com.sdesrd.filetransfer</groupId>
    <artifactId>file-transfer-client-sdk</artifactId>
    <version>3.0.0</version>
</dependency>
```

### 步骤2：简化配置代码

#### 基础配置迁移

**旧代码：**
```java
ClientAuthConfig authConfig = new ClientAuthConfig();
authConfig.setServerAddr("example.com");
authConfig.setUser("user");
authConfig.setSecretKey("secret");

ClientConfig config = new ClientConfigBuilder()
    .auth(authConfig)
    .build();
```

**新代码：**
```java
ClientConfig config = ClientConfigBuilder.create()
    .serverAddr("example.com")
    .auth("user", "secret")
    .build();
```

#### 完整配置迁移

**旧代码：**
```java
ClientAuthConfig authConfig = new ClientAuthConfig();
authConfig.setServerAddr("api.example.com");
authConfig.setServerPort(8443);
authConfig.setUseHttps(true);
authConfig.setUser("admin");
authConfig.setSecretKey("super-secret");

ClientConfig config = new ClientConfigBuilder()
    .auth(authConfig)
    .chunkSize(4 * 1024 * 1024)
    .maxConcurrentTransfers(5)
    .retry(3, 2000)
    .build();

config.setConnectTimeoutSeconds(60);
config.setReadTimeoutSeconds(120);
config.setWriteTimeoutSeconds(180);
```

**新代码：**
```java
ClientConfig config = ClientConfigBuilder.create()
    .serverAddr("api.example.com")
    .serverPort(8443)
    .useHttps()
    .auth("admin", "super-secret")
    .chunkSize(4 * 1024 * 1024)
    .connectTimeout(60)
    .readTimeout(120)
    .writeTimeout(180)
    .maxConcurrentTransfers(5)
    .retry(3, 2000)
    .build();
```

### 步骤3：使用便捷配置方法

#### 快速连接

```java
// 最简配置 - 只需要3个参数
ClientConfig config = ClientConfigBuilder.quickConnect(
    "api.example.com", "user", "secret");
```

#### 预设环境配置

```java
// 本地开发环境
ClientConfig localConfig = ClientConfigBuilder.localConfig("dev", "dev-secret");

// 生产环境（自动启用HTTPS，优化参数）
ClientConfig prodConfig = ClientConfigBuilder.productionConfig(
    "prod.example.com", 443, "prod-user", "prod-secret");

// 高性能配置（大文件传输优化）
ClientConfig highPerfConfig = ClientConfigBuilder.highPerformanceConfig(
    "fast.example.com", "user", "secret");
```

## 新增功能

### 1. 统一超时配置

```java
// 一次设置所有超时时间
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .timeouts(120)  // 连接、读取、写入超时都设为120秒
    .build();

// 或分别设置
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .connectTimeout(30)
    .readTimeout(60)
    .writeTimeout(90)
    .build();
```

### 2. 更多并发控制选项

```java
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .maxConcurrentTransfers(8)     // 并发传输数
    .maxIdleConnections(12)        // 连接池大小
    .keepAliveDuration(10)         // 连接保活时间（分钟）
    .build();
```

### 3. 灵活的重试配置

```java
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .retry(5, 1000)        // 5次重试，间隔1秒
    .build();

// 或分别设置
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .retryCount(3)
    .retryInterval(2000)   // 2秒间隔
    .build();
```

## 向后兼容性

### 现有代码无需修改

如果您暂时不想修改现有代码，3.0.0版本完全向后兼容：

```java
// 这些代码在3.0.0中仍然正常工作
ClientAuthConfig authConfig = new ClientAuthConfig();
// ... 设置认证配置

ClientConfig config = new ClientConfigBuilder()
    .auth(authConfig)  // 虽然标记为@Deprecated，但仍可使用
    .build();

// 通过getAuth()访问也仍然支持
String serverUrl = config.getAuth().getServerUrl();
```

### 逐步迁移建议

1. **新项目**: 直接使用新的统一配置API
2. **现有项目**: 在下次重构时逐步迁移到新API
3. **混合使用**: 可以在同一项目中混合使用新旧API

## 废弃提醒

以下方法已标记为`@Deprecated`，将在4.0.0版本中移除：

- `ClientConfig.getAuth()`
- `ClientConfig.setAuth(ClientAuthConfig)`
- `ClientConfigBuilder.auth(ClientAuthConfig)`

建议在方便时迁移到新API，避免在4.0.0升级时出现编译错误。

## 常见问题

### Q: 为什么要合并配置？

A: 原来的分离设计虽然理论上符合关注点分离原则，但在实际使用中增加了不必要的复杂性。99%的使用场景都是同时配置认证和传输参数，分离反而降低了开发效率。

### Q: 合并后会影响性能吗？

A: 不会。配置对象在客户端初始化时创建一次，对运行时性能没有影响。

### Q: 如何处理复杂的配置需求？

A: 新API提供了更多的配置方法和预设模板，反而能更好地满足复杂需求。如果有特殊需求，可以通过继承或组合的方式扩展。

### Q: 升级到3.0.0需要注意什么？

A: 向后兼容，现有代码无需修改。建议逐步迁移到新API以获得更好的开发体验。

## 示例代码库

完整的示例代码可以在以下文件中找到：

- `file-transfer-client-sdk/src/test/java/com/sdesrd/filetransfer/client/config/ClientConfigTest.java`
- `file-transfer-client-sdk/src/test/java/com/sdesrd/filetransfer/client/config/ClientConfigBuilderTest.java`
- `file-transfer-client-demo/src/main/java/com/sdesrd/filetransfer/demo/FileTransferClientDemo.java`

---

**👍 新的统一配置让文件传输SDK更易用！如有问题，请参考测试用例或提交Issue。** 