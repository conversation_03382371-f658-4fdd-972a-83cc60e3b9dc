package com.sdesrd.filetransfer.demo;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;

// 注意：客户端演示程序不直接依赖ULID库，而是手动生成示例ULID
import com.sdesrd.filetransfer.client.FileTransferClient;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.dto.TransferProgress;

import lombok.extern.slf4j.Slf4j;

/**
 * 客户端指定fileId功能演示程序
 * 展示如何使用客户端指定fileId模式进行文件上传
 * 
 * <AUTHOR>
 * @since 2.1.0
 */
@Slf4j
public class ClientSpecifiedFileIdDemo {

    /** 演示信息前缀 */
    private static final String DEMO_PREFIX = "[CLIENT_SPECIFIED_FILEID_DEMO]";

    /** 服务端URL */
    private static final String SERVER_URL = "http://localhost:8080/filetransfer";

    /** 测试用户名 */
    private static final String USERNAME = "testuser";

    /** 测试用户密钥 */
    private static final String SECRET_KEY = "test-secret-key";

    public static void main(String[] args) {
        log.info("{} 开始客户端指定fileId功能演示", DEMO_PREFIX);

        ClientSpecifiedFileIdDemo demo = new ClientSpecifiedFileIdDemo();
        
        try {
            // 创建测试文件
            demo.createTestFiles();
            
            // 演示客户端指定fileId上传
            demo.demonstrateClientSpecifiedFileIdUpload();
            
            // 演示fileId冲突检测
            demo.demonstrateFileIdConflictDetection();
            
            // 演示ULID格式验证
            demo.demonstrateUlidValidation();
            
            log.info("{} 客户端指定fileId功能演示完成", DEMO_PREFIX);
            
        } catch (Exception e) {
            log.error("{} 演示过程中发生错误", DEMO_PREFIX, e);
            System.exit(1);
        }
    }

    /**
     * 创建测试文件
     */
    private void createTestFiles() throws IOException {
        log.info("{} 创建测试文件", DEMO_PREFIX);

        Path testDir = Paths.get("test-files");
        if (!Files.exists(testDir)) {
            Files.createDirectories(testDir);
        }

        // 创建测试文件1
        Path testFile1 = testDir.resolve("test-file-1.txt");
        Files.write(testFile1, "这是测试文件1的内容，用于演示客户端指定fileId功能。".getBytes("UTF-8"));
        log.info("{} 创建测试文件: {}", DEMO_PREFIX, testFile1);

        // 创建测试文件2
        Path testFile2 = testDir.resolve("test-file-2.txt");
        Files.write(testFile2, "这是测试文件2的内容，用于演示fileId冲突检测。".getBytes("UTF-8"));
        log.info("{} 创建测试文件: {}", DEMO_PREFIX, testFile2);

        // 创建测试文件3（与文件1内容相同）
        Path testFile3 = testDir.resolve("test-file-3.txt");
        Files.write(testFile3, "这是测试文件1的内容，用于演示客户端指定fileId功能。".getBytes("UTF-8"));
        log.info("{} 创建测试文件: {}", DEMO_PREFIX, testFile3);
    }

    /**
     * 演示客户端指定fileId上传
     */
    private void demonstrateClientSpecifiedFileIdUpload() {
        log.info("{} 演示客户端指定fileId上传", DEMO_PREFIX);

        try (FileTransferClient client = createClient()) {
            // 使用示例ULID作为fileId（在实际应用中应该使用ULID库生成）
            String fileId = generateExampleUlid();
            log.info("{} 使用示例ULID作为fileId: {}", DEMO_PREFIX, fileId);

            // 创建传输监听器
            TransferListener listener = new TransferListener() {
                @Override
                public void onStart(TransferProgress progress) {
                    log.info("{} 开始上传 - 文件: {}, 大小: {} bytes",
                        DEMO_PREFIX, progress.getFileName(), progress.getTotalSize());
                }

                @Override
                public void onProgress(TransferProgress progress) {
                    log.info("{} 上传进度 - 文件: {}, 进度: {:.1f}%",
                        DEMO_PREFIX, progress.getFileName(), progress.getProgress());
                }

                @Override
                public void onCompleted(TransferProgress progress) {
                    log.info("{} 上传完成 - 文件: {}, 总大小: {} bytes",
                        DEMO_PREFIX, progress.getFileName(), progress.getTotalSize());
                }

                @Override
                public void onError(TransferProgress progress, Throwable error) {
                    log.error("{} 上传失败", DEMO_PREFIX, error);
                }
            };

            // 使用客户端指定fileId上传文件
            CompletableFuture<UploadResult> uploadFuture = client.uploadFileWithId(
                "test-files/test-file-1.txt",
                fileId,
                listener
            );

            UploadResult result = uploadFuture.get();
            
            if (result.isSuccess()) {
                log.info("{} 客户端指定fileId上传成功:", DEMO_PREFIX);
                log.info("  - 传输ID: {}", result.getTransferId());
                log.info("  - 文件ID: {}", result.getFileId());
                log.info("  - 文件名: {}", result.getFileName());
                log.info("  - 文件大小: {} bytes", result.getFileSize());
                log.info("  - 是否秒传: {}", result.isFastUpload());
            } else {
                log.error("{} 客户端指定fileId上传失败", DEMO_PREFIX);
            }

        } catch (Exception e) {
            log.error("{} 客户端指定fileId上传演示失败", DEMO_PREFIX, e);
        }
    }

    /**
     * 演示fileId冲突检测
     */
    private void demonstrateFileIdConflictDetection() {
        log.info("{} 演示fileId冲突检测", DEMO_PREFIX);

        try (FileTransferClient client = createClient()) {
            // 使用相同的fileId上传不同内容的文件
            String sameFileId = generateExampleUlid();
            log.info("{} 使用相同fileId上传不同文件: {}", DEMO_PREFIX, sameFileId);

            // 第一次上传
            log.info("{} 第一次上传文件1", DEMO_PREFIX);
            CompletableFuture<UploadResult> upload1 = client.uploadFileWithId(
                "test-files/test-file-1.txt",
                sameFileId,
                null
            );
            UploadResult result1 = upload1.get();
            log.info("{} 第一次上传结果: {}", DEMO_PREFIX, result1.isSuccess() ? "成功" : "失败");

            // 第二次上传（相同fileId，不同内容）
            log.info("{} 第二次上传文件2（相同fileId，不同内容）", DEMO_PREFIX);
            try {
                CompletableFuture<UploadResult> upload2 = client.uploadFileWithId(
                    "test-files/test-file-2.txt",
                    sameFileId,
                    null
                );
                UploadResult result2 = upload2.get();
                log.warn("{} 第二次上传意外成功，应该被拒绝", DEMO_PREFIX);
            } catch (Exception e) {
                log.info("{} 第二次上传被正确拒绝: {}", DEMO_PREFIX, e.getMessage());
            }

            // 第三次上传（相同fileId，相同内容）
            log.info("{} 第三次上传文件3（相同fileId，相同内容）", DEMO_PREFIX);
            CompletableFuture<UploadResult> upload3 = client.uploadFileWithId(
                "test-files/test-file-3.txt",
                sameFileId,
                null
            );
            UploadResult result3 = upload3.get();
            log.info("{} 第三次上传结果: {}, 秒传: {}", 
                DEMO_PREFIX, result3.isSuccess() ? "成功" : "失败", result3.isFastUpload());

        } catch (Exception e) {
            log.error("{} fileId冲突检测演示失败", DEMO_PREFIX, e);
        }
    }

    /**
     * 演示ULID格式验证
     */
    private void demonstrateUlidValidation() {
        log.info("{} 演示ULID格式验证", DEMO_PREFIX);

        try (FileTransferClient client = createClient()) {
            // 测试无效的fileId格式
            String[] invalidFileIds = {
                "invalid-format",
                "01ARZ3NDEKTSV4RRFFQ69G5FA", // 25字符
                "01ARZ3NDEKTSV4RRFFQ69G5FAVX", // 27字符
                "01ARZ3NDEKTSV4RRFFQ69G5FAI", // 包含I
                "01ARZ3NDEKTSV4RRFFQ69G5FAL", // 包含L
                "01ARZ3NDEKTSV4RRFFQ69G5FAO", // 包含O
                "01ARZ3NDEKTSV4RRFFQ69G5FAU"  // 包含U
            };

            for (String invalidFileId : invalidFileIds) {
                log.info("{} 测试无效fileId: {}", DEMO_PREFIX, invalidFileId);
                try {
                    CompletableFuture<UploadResult> upload = client.uploadFileWithId(
                        "test-files/test-file-1.txt",
                        invalidFileId,
                        null
                    );
                    upload.get();
                    log.warn("{} 无效fileId意外通过验证: {}", DEMO_PREFIX, invalidFileId);
                } catch (Exception e) {
                    log.info("{} 无效fileId被正确拒绝: {} - {}", 
                        DEMO_PREFIX, invalidFileId, e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("{} ULID格式验证演示失败", DEMO_PREFIX, e);
        }
    }

    /**
     * 创建文件传输客户端
     */
    private FileTransferClient createClient() {
        ClientConfig config = new ClientConfig();
        config.setServerAddr("localhost");
        config.setServerPort(8080);
        config.setContextPath("filetransfer");
        config.setUser(USERNAME);
        config.setSecretKey(SECRET_KEY);
        config.setChunkSize(1024 * 1024); // 1MB
        config.setMaxConcurrentTransfers(3);
        config.setConnectTimeoutSeconds(30);
        config.setReadTimeoutSeconds(60);

        return new FileTransferClient(config);
    }

    /**
     * 生成示例ULID
     * 注意：这只是演示用的简单实现，实际应用中应该使用专业的ULID库
     */
    private static String generateExampleUlid() {
        // 使用当前时间戳和随机数生成类似ULID的字符串
        long timestamp = System.currentTimeMillis();
        String timestampPart = Long.toString(timestamp, 32).toUpperCase();

        // 补齐到10位
        while (timestampPart.length() < 10) {
            timestampPart = "0" + timestampPart;
        }

        // 生成16位随机部分
        StringBuilder randomPart = new StringBuilder();
        String chars = "0123456789ABCDEFGHJKMNPQRSTVWXYZ";
        for (int i = 0; i < 16; i++) {
            randomPart.append(chars.charAt((int) (Math.random() * chars.length())));
        }

        return timestampPart + randomPart.toString();
    }
}
