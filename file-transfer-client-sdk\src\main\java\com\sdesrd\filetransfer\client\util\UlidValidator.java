package com.sdesrd.filetransfer.client.util;

import java.util.regex.Pattern;

import com.sdesrd.filetransfer.client.exception.FileTransferException;

/**
 * ULID验证工具类
 * 提供客户端ULID格式验证功能，确保传入的fileId符合ULID标准
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
public class UlidValidator {
    
    /**
     * ULID格式正则表达式
     * ULID格式：26个字符，使用Crockford's Base32编码
     * 字符集：0123456789ABCDEFGHJKMNPQRSTVWXYZ（排除I、L、O、U）
     */
    private static final Pattern ULID_PATTERN = Pattern.compile("^[0-9A-HJKMNP-TV-Z]{26}$");
    
    /**
     * ULID长度常量
     */
    private static final int ULID_LENGTH = 26;
    
    /**
     * 验证ULID格式是否有效
     * 
     * @param ulid 待验证的ULID字符串
     * @return 是否为有效的ULID格式
     */
    public static boolean isValidUlid(String ulid) {
        if (ulid == null || ulid.trim().isEmpty()) {
            return false;
        }
        
        String trimmedUlid = ulid.trim().toUpperCase();
        
        // 检查长度
        if (trimmedUlid.length() != ULID_LENGTH) {
            return false;
        }
        
        // 检查格式
        return ULID_PATTERN.matcher(trimmedUlid).matches();
    }
    
    /**
     * 验证并规范化ULID
     * 如果ULID格式无效，抛出异常
     * 
     * @param ulid 待验证的ULID字符串
     * @return 规范化后的ULID（大写）
     * @throws FileTransferException 如果ULID格式无效
     */
    public static String validateAndNormalize(String ulid) throws FileTransferException {
        if (ulid == null || ulid.trim().isEmpty()) {
            throw new FileTransferException("文件ID不能为空");
        }
        
        String trimmedUlid = ulid.trim().toUpperCase();
        
        // 检查长度
        if (trimmedUlid.length() != ULID_LENGTH) {
            throw new FileTransferException(
                String.format("文件ID长度无效：期望%d个字符，实际%d个字符", ULID_LENGTH, trimmedUlid.length())
            );
        }
        
        // 检查格式
        if (!ULID_PATTERN.matcher(trimmedUlid).matches()) {
            throw new FileTransferException(
                "文件ID格式无效：必须是26个字符的ULID格式，仅包含字符0-9和A-Z（排除I、L、O、U）"
            );
        }
        
        return trimmedUlid;
    }
    
    /**
     * 验证ULID并提供详细的错误信息
     * 
     * @param ulid 待验证的ULID字符串
     * @param fieldName 字段名称，用于错误信息
     * @throws FileTransferException 如果ULID格式无效
     */
    public static void validateUlidWithDetails(String ulid, String fieldName) throws FileTransferException {
        if (ulid == null || ulid.trim().isEmpty()) {
            throw new FileTransferException(String.format("%s不能为空", fieldName));
        }
        
        String trimmedUlid = ulid.trim();
        
        // 检查长度
        if (trimmedUlid.length() != ULID_LENGTH) {
            throw new FileTransferException(
                String.format("%s长度无效：期望%d个字符，实际%d个字符。ULID必须是26个字符长度。", 
                    fieldName, ULID_LENGTH, trimmedUlid.length())
            );
        }
        
        // 检查字符集
        String upperUlid = trimmedUlid.toUpperCase();
        if (!ULID_PATTERN.matcher(upperUlid).matches()) {
            // 找出无效字符
            StringBuilder invalidChars = new StringBuilder();
            for (char c : trimmedUlid.toCharArray()) {
                char upperC = Character.toUpperCase(c);
                if (!isValidUlidChar(upperC)) {
                    if (invalidChars.length() > 0) {
                        invalidChars.append(", ");
                    }
                    invalidChars.append("'").append(c).append("'");
                }
            }
            
            throw new FileTransferException(
                String.format("%s格式无效：包含无效字符%s。ULID仅支持字符0-9和A-Z（排除I、L、O、U）。", 
                    fieldName, invalidChars.toString())
            );
        }
    }
    
    /**
     * 检查字符是否为有效的ULID字符
     * 
     * @param c 待检查的字符
     * @return 是否为有效字符
     */
    private static boolean isValidUlidChar(char c) {
        return (c >= '0' && c <= '9') || 
               (c >= 'A' && c <= 'H') || 
               (c >= 'J' && c <= 'K') || 
               (c >= 'M' && c <= 'N') || 
               (c >= 'P' && c <= 'T') || 
               (c >= 'V' && c <= 'Z');
    }
    
    /**
     * 生成ULID格式说明
     * 
     * @return ULID格式说明字符串
     */
    public static String getUlidFormatDescription() {
        return "ULID格式要求：26个字符长度，使用Crockford's Base32编码，" +
               "字符集为0-9和A-Z（排除I、L、O、U以避免混淆）";
    }
}
