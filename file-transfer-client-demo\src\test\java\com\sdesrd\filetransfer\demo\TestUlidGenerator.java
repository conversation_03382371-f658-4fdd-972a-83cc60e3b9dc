package com.sdesrd.filetransfer.demo;

import java.security.SecureRandom;
import java.time.Instant;

/**
 * 测试用ULID生成器
 * 为测试环境提供符合ULID标准的文件ID生成功能
 * 
 * <AUTHOR>
 * @since 2.1.0
 */
public class TestUlidGenerator {
    
    /** ULID字符集（Crockford's Base32，排除I、L、O、U） */
    private static final String ULID_CHARS = "0123456789ABCDEFGHJKMNPQRSTVWXYZ";
    
    /** ULID总长度 */
    private static final int ULID_LENGTH = 26;
    
    /** 时间戳部分长度 */
    private static final int TIMESTAMP_LENGTH = 10;
    
    /** 随机部分长度 */
    private static final int RANDOM_LENGTH = 16;
    
    /** 安全随机数生成器 */
    private static final SecureRandom RANDOM = new SecureRandom();
    
    /**
     * 生成测试用ULID
     * 
     * @return 符合ULID标准的26字符字符串
     */
    public static String generateTestUlid() {
        return generateTestUlid(System.currentTimeMillis());
    }
    
    /**
     * 生成指定时间戳的测试用ULID
     * 
     * @param timestamp 时间戳（毫秒）
     * @return 符合ULID标准的26字符字符串
     */
    public static String generateTestUlid(long timestamp) {
        StringBuilder ulid = new StringBuilder(ULID_LENGTH);
        
        // 生成时间戳部分（10字符）
        String timestampPart = encodeTimestamp(timestamp);
        ulid.append(timestampPart);
        
        // 生成随机部分（16字符）
        String randomPart = generateRandomPart();
        ulid.append(randomPart);
        
        return ulid.toString();
    }
    
    /**
     * 编码时间戳为ULID格式
     * 
     * @param timestamp 时间戳（毫秒）
     * @return 10字符的时间戳编码
     */
    private static String encodeTimestamp(long timestamp) {
        StringBuilder result = new StringBuilder(TIMESTAMP_LENGTH);
        long value = timestamp;
        
        // 使用Base32编码时间戳
        for (int i = 0; i < TIMESTAMP_LENGTH; i++) {
            result.insert(0, ULID_CHARS.charAt((int) (value % 32)));
            value /= 32;
        }
        
        // 确保长度为10
        while (result.length() < TIMESTAMP_LENGTH) {
            result.insert(0, '0');
        }
        
        return result.toString();
    }
    
    /**
     * 生成随机部分
     * 
     * @return 16字符的随机编码
     */
    private static String generateRandomPart() {
        StringBuilder result = new StringBuilder(RANDOM_LENGTH);
        
        for (int i = 0; i < RANDOM_LENGTH; i++) {
            int index = RANDOM.nextInt(ULID_CHARS.length());
            result.append(ULID_CHARS.charAt(index));
        }
        
        return result.toString();
    }
    
    /**
     * 验证ULID格式是否有效
     * 
     * @param ulid 待验证的ULID字符串
     * @return 是否为有效的ULID格式
     */
    public static boolean isValidUlid(String ulid) {
        if (ulid == null || ulid.length() != ULID_LENGTH) {
            return false;
        }
        
        // 检查所有字符是否在有效字符集中
        for (char c : ulid.toCharArray()) {
            if (ULID_CHARS.indexOf(c) == -1) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 生成多个不同的测试ULID
     * 
     * @param count 生成数量
     * @return ULID数组
     */
    public static String[] generateMultipleTestUlids(int count) {
        String[] ulids = new String[count];
        long baseTimestamp = System.currentTimeMillis();
        
        for (int i = 0; i < count; i++) {
            // 使用不同的时间戳确保ULID不重复
            ulids[i] = generateTestUlid(baseTimestamp + i);
        }
        
        return ulids;
    }
    
    /**
     * 生成用于测试冲突检测的ULID对
     * 
     * @return 包含两个不同ULID的数组
     */
    public static String[] generateConflictTestUlids() {
        return generateMultipleTestUlids(2);
    }
    
    /**
     * 生成用于测试边界情况的特殊ULID
     * 
     * @return 特殊格式的ULID
     */
    public static String generateBoundaryTestUlid() {
        // 生成一个使用最小时间戳的ULID
        return generateTestUlid(0L);
    }
    
    /**
     * 获取ULID格式说明
     * 
     * @return ULID格式说明字符串
     */
    public static String getUlidFormatDescription() {
        return String.format("ULID格式：%d个字符，使用Crockford's Base32编码（字符集：%s）", 
            ULID_LENGTH, ULID_CHARS);
    }
}
