package com.sdesrd.filetransfer.server.dto;

import com.sdesrd.filetransfer.server.service.FileTransferMonitorService.TransferStatistics;
import lombok.Data;

/**
 * 系统健康检查响应DTO
 * 包含系统状态、JVM信息和传输统计
 */
@Data
public class SystemHealthResponse {
    
    /**
     * 系统状态：UP/DOWN
     */
    private String status;
    
    /**
     * 检查时间戳
     */
    private Long timestamp;
    
    /**
     * JVM总内存（字节）
     */
    private Long totalMemory;
    
    /**
     * JVM空闲内存（字节）
     */
    private Long freeMemory;
    
    /**
     * JVM已使用内存（字节）
     */
    private Long usedMemory;
    
    /**
     * JVM最大内存（字节）
     */
    private Long maxMemory;
    
    /**
     * 传输统计信息
     */
    private TransferStatistics transferStats;
    
    /**
     * 获取内存使用率（百分比）
     */
    public double getMemoryUsagePercentage() {
        if (maxMemory == null || maxMemory == 0) {
            return 0.0;
        }
        return (double) usedMemory / maxMemory * 100;
    }
    
    /**
     * 获取格式化的内存信息
     */
    public String getFormattedMemoryInfo() {
        return String.format("已使用: %dMB / 最大: %dMB (%.2f%%)", 
                usedMemory / 1024 / 1024, 
                maxMemory / 1024 / 1024, 
                getMemoryUsagePercentage());
    }
}
