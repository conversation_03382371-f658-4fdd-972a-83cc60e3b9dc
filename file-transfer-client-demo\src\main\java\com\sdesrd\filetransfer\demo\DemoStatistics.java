package com.sdesrd.filetransfer.demo;

import com.sdesrd.filetransfer.client.util.FileUtils;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 演示统计信息类
 * 
 * 用于收集和展示演示过程中的各种统计信息
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-06-19
 */
public class DemoStatistics {

    /** 上传统计 */
    private final AtomicInteger uploadSuccessCount = new AtomicInteger(0);
    private final AtomicInteger uploadFailureCount = new AtomicInteger(0);
    private final AtomicLong totalUploadBytes = new AtomicLong(0);
    private final AtomicLong totalUploadTime = new AtomicLong(0);
    
    /** 下载统计 */
    private final AtomicInteger downloadSuccessCount = new AtomicInteger(0);
    private final AtomicInteger downloadFailureCount = new AtomicInteger(0);
    private final AtomicLong totalDownloadBytes = new AtomicLong(0);
    private final AtomicLong totalDownloadTime = new AtomicLong(0);
    
    /** 文件信息查询统计 */
    private final AtomicInteger infoQuerySuccessCount = new AtomicInteger(0);
    private final AtomicInteger infoQueryFailureCount = new AtomicInteger(0);
    
    /** 删除操作统计 */
    private final AtomicInteger deleteSuccessCount = new AtomicInteger(0);
    private final AtomicInteger deleteFailureCount = new AtomicInteger(0);
    
    /** 错误处理统计 */
    private final AtomicInteger errorHandlingTestCount = new AtomicInteger(0);
    
    /** 性能测试统计 */
    private final AtomicInteger performanceTestCount = new AtomicInteger(0);
    private final AtomicLong maxUploadSpeed = new AtomicLong(0);
    private final AtomicLong maxDownloadSpeed = new AtomicLong(0);
    
    /** 并发传输统计 */
    private final AtomicInteger concurrentTransferCount = new AtomicInteger(0);
    
    /** 断点续传统计 */
    private final AtomicInteger resumeTransferCount = new AtomicInteger(0);

    /**
     * 记录上传成功
     * 
     * @param fileSize 文件大小（字节）
     * @param duration 传输耗时（毫秒）
     */
    public void recordUploadSuccess(long fileSize, long duration) {
        uploadSuccessCount.incrementAndGet();
        totalUploadBytes.addAndGet(fileSize);
        totalUploadTime.addAndGet(duration);
        
        // 计算并更新最大上传速度
        if (duration > 0) {
            long speed = (fileSize * 1000) / duration; // 字节/秒
            updateMaxUploadSpeed(speed);
        }
    }

    /**
     * 记录上传失败
     */
    public void recordUploadFailure() {
        uploadFailureCount.incrementAndGet();
    }

    /**
     * 记录下载成功
     * 
     * @param fileSize 文件大小（字节）
     * @param duration 传输耗时（毫秒）
     */
    public void recordDownloadSuccess(long fileSize, long duration) {
        downloadSuccessCount.incrementAndGet();
        totalDownloadBytes.addAndGet(fileSize);
        totalDownloadTime.addAndGet(duration);
        
        // 计算并更新最大下载速度
        if (duration > 0) {
            long speed = (fileSize * 1000) / duration; // 字节/秒
            updateMaxDownloadSpeed(speed);
        }
    }

    /**
     * 记录下载失败
     */
    public void recordDownloadFailure() {
        downloadFailureCount.incrementAndGet();
    }

    /**
     * 记录文件信息查询成功
     */
    public void recordInfoQuerySuccess() {
        infoQuerySuccessCount.incrementAndGet();
    }

    /**
     * 记录文件信息查询失败
     */
    public void recordInfoQueryFailure() {
        infoQueryFailureCount.incrementAndGet();
    }

    /**
     * 记录删除操作成功
     */
    public void recordDeleteSuccess() {
        deleteSuccessCount.incrementAndGet();
    }

    /**
     * 记录删除操作失败
     */
    public void recordDeleteFailure() {
        deleteFailureCount.incrementAndGet();
    }

    /**
     * 记录错误处理测试
     */
    public void recordErrorHandlingTest() {
        errorHandlingTestCount.incrementAndGet();
    }

    /**
     * 记录性能测试
     */
    public void recordPerformanceTest() {
        performanceTestCount.incrementAndGet();
    }

    /**
     * 记录并发传输
     */
    public void recordConcurrentTransfer() {
        concurrentTransferCount.incrementAndGet();
    }

    /**
     * 记录断点续传
     */
    public void recordResumeTransfer() {
        resumeTransferCount.incrementAndGet();
    }

    /**
     * 更新最大上传速度
     * 
     * @param speed 速度（字节/秒）
     */
    private void updateMaxUploadSpeed(long speed) {
        long currentMax = maxUploadSpeed.get();
        while (speed > currentMax && !maxUploadSpeed.compareAndSet(currentMax, speed)) {
            currentMax = maxUploadSpeed.get();
        }
    }

    /**
     * 更新最大下载速度
     * 
     * @param speed 速度（字节/秒）
     */
    private void updateMaxDownloadSpeed(long speed) {
        long currentMax = maxDownloadSpeed.get();
        while (speed > currentMax && !maxDownloadSpeed.compareAndSet(currentMax, speed)) {
            currentMax = maxDownloadSpeed.get();
        }
    }

    /**
     * 打印统计信息
     */
    public void printStatistics() {
        System.out.println("上传统计:");
        System.out.println("  成功次数: " + uploadSuccessCount.get());
        System.out.println("  失败次数: " + uploadFailureCount.get());
        System.out.println("  总上传量: " + FileUtils.formatFileSize(totalUploadBytes.get()));
        System.out.println("  总耗时: " + totalUploadTime.get() + "ms");
        
        if (uploadSuccessCount.get() > 0 && totalUploadTime.get() > 0) {
            long avgSpeed = (totalUploadBytes.get() * 1000) / totalUploadTime.get();
            System.out.println("  平均速度: " + FileUtils.formatFileSize(avgSpeed) + "/s");
        }
        
        if (maxUploadSpeed.get() > 0) {
            System.out.println("  最大速度: " + FileUtils.formatFileSize(maxUploadSpeed.get()) + "/s");
        }
        
        System.out.println();
        
        System.out.println("下载统计:");
        System.out.println("  成功次数: " + downloadSuccessCount.get());
        System.out.println("  失败次数: " + downloadFailureCount.get());
        System.out.println("  总下载量: " + FileUtils.formatFileSize(totalDownloadBytes.get()));
        System.out.println("  总耗时: " + totalDownloadTime.get() + "ms");
        
        if (downloadSuccessCount.get() > 0 && totalDownloadTime.get() > 0) {
            long avgSpeed = (totalDownloadBytes.get() * 1000) / totalDownloadTime.get();
            System.out.println("  平均速度: " + FileUtils.formatFileSize(avgSpeed) + "/s");
        }
        
        if (maxDownloadSpeed.get() > 0) {
            System.out.println("  最大速度: " + FileUtils.formatFileSize(maxDownloadSpeed.get()) + "/s");
        }
        
        System.out.println();
        
        System.out.println("其他操作统计:");
        System.out.println("  信息查询成功: " + infoQuerySuccessCount.get());
        System.out.println("  信息查询失败: " + infoQueryFailureCount.get());
        System.out.println("  删除操作成功: " + deleteSuccessCount.get());
        System.out.println("  删除操作失败: " + deleteFailureCount.get());
        System.out.println("  错误处理测试: " + errorHandlingTestCount.get());
        System.out.println("  性能测试次数: " + performanceTestCount.get());
        System.out.println("  并发传输次数: " + concurrentTransferCount.get());
        System.out.println("  断点续传次数: " + resumeTransferCount.get());
        
        System.out.println();
        
        // 计算总体成功率
        int totalOperations = uploadSuccessCount.get() + uploadFailureCount.get() + 
                             downloadSuccessCount.get() + downloadFailureCount.get() +
                             infoQuerySuccessCount.get() + infoQueryFailureCount.get() +
                             deleteSuccessCount.get() + deleteFailureCount.get();
        
        int totalSuccesses = uploadSuccessCount.get() + downloadSuccessCount.get() + 
                            infoQuerySuccessCount.get() + deleteSuccessCount.get();
        
        if (totalOperations > 0) {
            double successRate = (double) totalSuccesses / totalOperations * 100;
            System.out.println("总体成功率: " + String.format("%.2f%%", successRate) + 
                             " (" + totalSuccesses + "/" + totalOperations + ")");
        }
    }

    /**
     * 重置所有统计信息
     */
    public void reset() {
        uploadSuccessCount.set(0);
        uploadFailureCount.set(0);
        totalUploadBytes.set(0);
        totalUploadTime.set(0);
        
        downloadSuccessCount.set(0);
        downloadFailureCount.set(0);
        totalDownloadBytes.set(0);
        totalDownloadTime.set(0);
        
        infoQuerySuccessCount.set(0);
        infoQueryFailureCount.set(0);
        
        deleteSuccessCount.set(0);
        deleteFailureCount.set(0);
        
        errorHandlingTestCount.set(0);
        performanceTestCount.set(0);
        maxUploadSpeed.set(0);
        maxDownloadSpeed.set(0);
        
        concurrentTransferCount.set(0);
        resumeTransferCount.set(0);
    }

    /**
     * 获取上传成功次数
     */
    public int getUploadSuccessCount() {
        return uploadSuccessCount.get();
    }

    /**
     * 获取上传失败次数
     */
    public int getUploadFailureCount() {
        return uploadFailureCount.get();
    }

    /**
     * 获取下载成功次数
     */
    public int getDownloadSuccessCount() {
        return downloadSuccessCount.get();
    }

    /**
     * 获取下载失败次数
     */
    public int getDownloadFailureCount() {
        return downloadFailureCount.get();
    }

    /**
     * 获取总上传字节数
     */
    public long getTotalUploadBytes() {
        return totalUploadBytes.get();
    }

    /**
     * 获取总下载字节数
     */
    public long getTotalDownloadBytes() {
        return totalDownloadBytes.get();
    }

    /**
     * 获取总上传时间
     */
    public long getTotalUploadTime() {
        return totalUploadTime.get();
    }

    /**
     * 获取总下载时间
     */
    public long getTotalDownloadTime() {
        return totalDownloadTime.get();
    }

    /**
     * 获取最大上传速度
     */
    public long getMaxUploadSpeed() {
        return maxUploadSpeed.get();
    }

    /**
     * 获取最大下载速度
     */
    public long getMaxDownloadSpeed() {
        return maxDownloadSpeed.get();
    }
}
