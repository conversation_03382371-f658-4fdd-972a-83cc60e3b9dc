package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * 文件上传完成响应
 * 返回文件的最终信息，包括fileId和相对路径
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class FileUploadCompleteResponse {
    
    /**
     * 传输ID
     */
    private String transferId;
    
    /**
     * 文件ID（ULID格式）
     */
    private String fileId;
    
    /**
     * 最终存储的文件名（格式：md5.{后缀名} 或 md5）
     */
    private String fileName;
    
    /**
     * 文件相对路径（相对于存储根目录）
     * 格式：YYYYMM/fileId/fileName
     */
    private String relativePath;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件MD5值
     */
    private String fileMd5;
    
    /**
     * 上传完成时间（ISO格式）
     */
    private String completeTime;
    
    /**
     * 是否为秒传
     */
    private Boolean fastUpload;
    
    /**
     * 传输耗时（毫秒）
     */
    private Long transferDuration;
}
