package com.sdesrd.filetransfer.server.controller;

import javax.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import com.sdesrd.filetransfer.server.dto.ApiResult;
import com.sdesrd.filetransfer.server.dto.SystemHealthResponse;
import com.sdesrd.filetransfer.server.interceptor.AuthInterceptor;
import com.sdesrd.filetransfer.server.service.FileTransferMonitorService;
import com.sdesrd.filetransfer.server.service.FileTransferMonitorService.TransferStatistics;
import com.sdesrd.filetransfer.server.service.FileTransferService;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;

/**
 * 文件传输管理控制器测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("文件传输管理控制器测试")
class FileTransferAdminControllerTest {

    private static final Logger log = LoggerFactory.getLogger(FileTransferAdminControllerTest.class);

    // 测试日志标识常量
    private static final String EXPECTED_EXCEPTION_PREFIX = "[EXPECTED_EXCEPTION_TEST]";
    private static final String TEST_INFO_PREFIX = "[TEST_INFO]";

    @Mock
    private FileTransferMonitorService monitorService;

    @Mock
    private FileTransferService fileTransferService;

    @Mock
    private HttpServletRequest httpRequest;

    @InjectMocks
    private FileTransferAdminController adminController;

    private TransferStatistics mockStatistics;
    
    @BeforeEach
    void setUp() {
        // 创建模拟的传输统计数据
        mockStatistics = new TransferStatistics();
        mockStatistics.setPendingCount(5);
        mockStatistics.setTransferringCount(10);
        mockStatistics.setCompletedCount(100);
        mockStatistics.setFailedCount(2);
        mockStatistics.setSuccessRate(98.04);
        
        // 使用ReflectionTestUtils确保Mock对象正确注入
        // 这是为了解决@Autowired字段在@InjectMocks中可能不生效的问题
        ReflectionTestUtils.setField(adminController, "monitorService", monitorService);
        ReflectionTestUtils.setField(adminController, "fileTransferService", fileTransferService);
    }
    
    @Test
    @DisplayName("获取传输统计信息 - 成功")
    void testGetStatistics_Success() {
        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class)) {
            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");
            
            when(monitorService.getTransferStatistics()).thenReturn(mockStatistics);
            
            // 执行测试
            ApiResult<TransferStatistics> result = adminController.getStatistics(httpRequest);
            
            // 验证结果
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            assertEquals(5, result.getData().getPendingCount());
            assertEquals(10, result.getData().getTransferringCount());
            assertEquals(100, result.getData().getCompletedCount());
            assertEquals(2, result.getData().getFailedCount());
            assertEquals(98.04, result.getData().getSuccessRate(), 0.01);
            
            // 验证方法调用
            verify(monitorService).getTransferStatistics();
        }
    }
    
    @Test
    @DisplayName("获取传输统计信息 - 异常处理")
    void testGetStatistics_Exception() {
        log.info("{} 开始测试获取统计信息异常处理 - 这将产生预期的RuntimeException", EXPECTED_EXCEPTION_PREFIX);

        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class)) {
            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");

            log.info("{} 模拟数据库连接失败异常 - 预期结果：捕获异常并返回错误响应", EXPECTED_EXCEPTION_PREFIX);
            when(monitorService.getTransferStatistics()).thenThrow(new RuntimeException("数据库连接失败"));

            // 执行测试
            ApiResult<TransferStatistics> result = adminController.getStatistics(httpRequest);

            // 验证结果
            assertFalse(result.isSuccess());
            assertTrue(result.getMessage().contains("获取传输统计信息失败"));
            assertNull(result.getData());

            log.info("{} 异常处理测试完成 - 异常已被正确捕获并处理", TEST_INFO_PREFIX);
        }
    }
    
    @Test
    @DisplayName("系统健康检查 - 成功")
    void testSystemHealth_Success() {
        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class)) {
            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");
            
            when(monitorService.getTransferStatistics()).thenReturn(mockStatistics);
            
            // 执行测试
            ApiResult<SystemHealthResponse> result = adminController.systemHealth(httpRequest);
            
            // 验证结果
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            
            SystemHealthResponse healthResponse = result.getData();
            assertEquals("UP", healthResponse.getStatus());
            assertNotNull(healthResponse.getTimestamp());
            assertNotNull(healthResponse.getTotalMemory());
            assertNotNull(healthResponse.getFreeMemory());
            assertNotNull(healthResponse.getUsedMemory());
            assertNotNull(healthResponse.getMaxMemory());
            assertNotNull(healthResponse.getTransferStats());
            
            // 验证传输统计数据
            assertEquals(mockStatistics.getCompletedCount(), healthResponse.getTransferStats().getCompletedCount());
            assertEquals(mockStatistics.getFailedCount(), healthResponse.getTransferStats().getFailedCount());
            
            // 验证方法调用
            verify(monitorService).getTransferStatistics();
        }
    }
    
    @Test
    @DisplayName("清理限流器缓存 - 成功")
    void testClearRateLimiters_Success() {
        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class);
             MockedStatic<RateLimitUtils> mockedRateLimitUtils = mockStatic(RateLimitUtils.class)) {
            
            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");
            
            // 执行测试
            ApiResult<String> result = adminController.clearRateLimiters(httpRequest);
            
            // 验证结果
            assertTrue(result.isSuccess());
            assertEquals("限流器缓存清理完成", result.getData());
            
            // 验证方法调用
            mockedRateLimitUtils.verify(RateLimitUtils::clearAllRateLimiters);
        }
    }
    
    @Test
    @DisplayName("清理限流器缓存 - 异常处理")
    void testClearRateLimiters_Exception() {
        log.info("{} 开始测试清理限流器缓存异常处理 - 这将产生预期的RuntimeException", EXPECTED_EXCEPTION_PREFIX);

        // 准备测试数据
        try (MockedStatic<AuthInterceptor> mockedAuthInterceptor = mockStatic(AuthInterceptor.class);
             MockedStatic<RateLimitUtils> mockedRateLimitUtils = mockStatic(RateLimitUtils.class)) {

            mockedAuthInterceptor.when(() -> AuthInterceptor.getCurrentUser(httpRequest))
                    .thenReturn("admin");

            log.info("{} 模拟限流器清理失败异常 - 预期结果：捕获异常并返回错误响应", EXPECTED_EXCEPTION_PREFIX);
            mockedRateLimitUtils.when(RateLimitUtils::clearAllRateLimiters)
                    .thenThrow(new RuntimeException("清理失败"));

            // 执行测试
            ApiResult<String> result = adminController.clearRateLimiters(httpRequest);

            // 验证结果
            assertFalse(result.isSuccess());
            assertTrue(result.getMessage().contains("清理限流器缓存失败"));

            log.info("{} 清理限流器异常处理测试完成 - 异常已被正确捕获并处理", TEST_INFO_PREFIX);
        }
    }
    
    // 注意：数据库重建相关测试已移除
    // 原因：为避免功能重复，数据库重建端点已从FileTransferAdminController移除
    // 所有数据库管理相关操作现在统一由DatabaseManagementController提供
    // 相关测试请参考DatabaseManagementControllerTest

    @Test
    @DisplayName("数据库重建端点移除验证")
    void testRebuildDatabaseEndpointRemoved() {
        log.info("{} 验证数据库重建端点已从管理控制器中移除", TEST_INFO_PREFIX);

        // 通过反射检查是否还存在rebuildDatabase方法
        java.lang.reflect.Method[] methods = adminController.getClass().getDeclaredMethods();
        boolean hasRebuildDatabaseMethod = false;

        for (java.lang.reflect.Method method : methods) {
            if ("rebuildDatabase".equals(method.getName())) {
                hasRebuildDatabaseMethod = true;
                break;
            }
        }

        assertFalse(hasRebuildDatabaseMethod,
                "FileTransferAdminController不应该再包含rebuildDatabase方法");

        log.info("{} 数据库重建端点移除验证通过 - 避免了功能重复", TEST_INFO_PREFIX);
    }



    
    @Test
    @DisplayName("内存使用率计算测试")
    void testMemoryUsageCalculation() {
        // 创建健康检查响应对象
        SystemHealthResponse healthResponse = new SystemHealthResponse();
        healthResponse.setUsedMemory(512 * 1024 * 1024L); // 512MB
        healthResponse.setMaxMemory(1024 * 1024 * 1024L); // 1GB
        
        // 验证内存使用率计算
        assertEquals(50.0, healthResponse.getMemoryUsagePercentage(), 0.01);
        
        // 验证格式化内存信息
        String formattedInfo = healthResponse.getFormattedMemoryInfo();
        assertTrue(formattedInfo.contains("512MB"));
        assertTrue(formattedInfo.contains("1024MB"));
        assertTrue(formattedInfo.contains("50.00%"));
    }
}
